import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

// Subject type definition
export interface Subject {
  id: string;
  name: string;
  description: string;
  code: string;
  courseId: string;
  teacherId?: string;
  credits: number;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

export const getSubjects = async (): Promise<Subject[]> => {
  try {
    console.log('Fetching subjects from API');
    const subjects = await apiClient.get<Subject[]>('/subjects');
    return subjects;
  } catch (error) {
    console.error('Error fetching subjects:', error);
    throw error;
  }
};

export const getSubjectsByCourse = async (courseId: string): Promise<Subject[]> => {
  try {
    console.log(`Fetching subjects for course: ${courseId}`);
    const subjects = await apiClient.get<Subject[]>(`/courses/${courseId}/subjects`);
    return subjects;
  } catch (error) {
    console.error('Error fetching subjects by course:', error);
    throw error;
  }
};

export const getSubject = async (id: string): Promise<Subject | null> => {
  try {
    console.log(`Fetching subject with ID: ${id}`);
    const subject = await apiClient.get<Subject>(`/subjects/${id}`);
    return subject;
  } catch (error) {
    console.error('Error fetching subject:', error);
    return null;
  }
};

export const createSubject = async (data: Omit<Subject, 'id' | 'created_at' | 'updated_at'>): Promise<Subject> => {
  try {
    console.log('Creating new subject:', data);

    const newSubject = await apiClient.post<Subject>('/subjects', data);

    await logActivity('subject_created', {
      subjectId: newSubject.id,
      name: data.name,
      code: data.code,
      courseId: data.courseId
    });

    toast.success('Subject created successfully');
    return newSubject;
  } catch (error: any) {
    console.error('Error creating subject:', error);
    toast.error(error.message || 'Failed to create subject');
    throw error;
  }
};

export const updateSubject = async (id: string, data: Partial<Subject>): Promise<Subject> => {
  try {
    console.log('Updating subject:', id, data);

    const updatedSubject = await apiClient.put<Subject>(`/subjects/${id}`, data);

    await logActivity('subject_updated', {
      subjectId: id,
      name: data.name,
      code: data.code,
      courseId: data.courseId
    });

    toast.success('Subject updated successfully');
    return updatedSubject;
  } catch (error: any) {
    console.error('Error updating subject:', error);
    toast.error(error.message || 'Failed to update subject');
    throw error;
  }
};

export const deleteSubject = async (id: string): Promise<boolean> => {
  try {
    console.log('Deleting subject:', id);

    // Get subject details before deletion for logging
    const subject = await getSubject(id);

    if (!subject) {
      toast.error('Subject not found');
      return false;
    }

    await apiClient.delete(`/subjects/${id}`);

    await logActivity('subject_deleted', {
      subjectId: id,
      name: subject.name,
      code: subject.code
    });

    toast.success(`Successfully deleted subject ${subject.name} (${subject.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting subject:', error);
    toast.error(error.message || 'Failed to delete subject');
    throw error;
  }
};