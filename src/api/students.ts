import type { Student as StudentType } from '@/types/student';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api-client';

export const getStudents = async () => {
  try {
    const students = await apiClient.get<StudentType[]>('/students');
    return students;
  } catch (error) {
    console.error('Error fetching students:', error);
    throw error;
  }
};

export const getStudentsByLevel = async (levelId: string) => {
  try {
    console.log('Fetching students for level:', levelId);
    const students = await apiClient.get<StudentType[]>(`/levels/${levelId}/students`);
    console.log(`Found ${students.length} students in level ${levelId}`);
    return students;
  } catch (error) {
    console.error('Error fetching students by level:', error);
    throw error;
  }
};

export const getStudent = async (id: string) => {
  try {
    const student = await apiClient.get<StudentType>(`/students/${id}`);
    return student;
  } catch (error) {
    console.error('Error fetching student:', error);
    throw error;
  }
};

export const updateStudent = async (id: string, data: Partial<StudentType>) => {
  try {
    // Remove undefined fields
    const cleanData = Object.fromEntries(
      Object.entries(data).filter(([_, value]) => value !== undefined)
    );

    await apiClient.put(`/students/${id}`, cleanData);
    toast.success('Student updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating student:', error);
    toast.error(error.message || 'Failed to update student');
    throw error;
  }
};

export const deleteStudent = async (id: string) => {
  try {
    // Get student details first
    const student = await getStudent(id);

    // Delete student record
    await apiClient.delete(`/students/${id}`);

    toast.success(`Successfully deleted student ${student.name} (${student.student_id})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting student:', error);
    toast.error(error.message || 'Failed to delete student');
    throw error;
  }
};

export const createStudent = async (data: Omit<StudentType, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const studentData = {
      ...data,
      date_of_registration: data.date_of_registration || new Date().toISOString(),
      enrollment_status: data.enrollment_status || 'Active',
      nationality: data.nationality || '',
      payment_status: data.payment_status || 'Pending'
    };

    const newStudent = await apiClient.post<StudentType>('/students', studentData);
    toast.success('Student created successfully');
    return newStudent;
  } catch (error: any) {
    console.error('Error creating student:', error);
    toast.error(error.message || 'Failed to create student');
    throw error;
  }
};

export const searchStudents = async (query: string) => {
  try {
    console.log('Searching students with query:', query);

    // Use query parameter to search on backend
    const params = new URLSearchParams();
    if (query && query.trim()) {
      params.append('search', query.trim());
    }
    params.append('status', 'Active');

    const students = await apiClient.get<StudentType[]>(`/students?${params.toString()}`);
    console.log(`Found ${students.length} students matching query`);
    return students;
  } catch (error) {
    console.error('Error searching students:', error);
    throw error;
  }
};

// ID Card related functions
export const updateIdCardStatus = async (studentId: string, status: string, expiryDate?: string) => {
  try {
    const updateData: Record<string, any> = {
      id_card_status: status
    };

    if (expiryDate) {
      updateData.id_card_expiry_date = expiryDate;
    }

    await updateStudent(studentId, updateData);
    return true;
  } catch (error: any) {
    console.error('Error updating ID card status:', error);
    throw error;
  }
};

export const checkAndUpdateExpiredIdCards = async () => {
  try {
    const allStudents = await getStudents();
    const currentDate = new Date();

    // Type assertion to handle dynamic ID card properties
    const studentsWithIdCard = allStudents as (StudentType & {
      id_card_expiry_date?: string;
      id_card_status?: string;
    })[];

    const updates = studentsWithIdCard
      .filter(student => {
        if (!student.id_card_expiry_date || student.id_card_status !== 'generated') {
          return false;
        }

        const expiryDate = new Date(student.id_card_expiry_date);
        return expiryDate < currentDate;
      })
      .map(student => updateIdCardStatus(student.id, 'expired'));

    await Promise.all(updates);
    return updates.length;
  } catch (error: any) {
    console.error('Error checking and updating expired ID cards:', error);
    throw error;
  }
};
