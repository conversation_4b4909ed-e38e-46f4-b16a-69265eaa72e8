import { apiClient } from '@/lib/api-client';
import { AuthService } from '@/lib/auth';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export interface Exam {
  id: string;
  name: string;
  subject: string;
  course_id: string;
  level_id: string;
  type: string;
  date: string;
  time: string;
  class: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface ExamResult {
  id: string;
  exam_id: string;
  student_id: string;
  marks: number;
  grade: string;
  remarks?: string;
  date_recorded: string;
  recorded_by: string;
}

export type ExamFormData = {
  name: string;
  subject: string;
  course_id: string;
  level_id: string;
  type: string;
  date: string;
  time: string;
  class: string;
  status?: string;
};

export const getExams = async (): Promise<Exam[]> => {
  try {
    console.log('Fetching exams from API');
    const exams = await apiClient.get<Exam[]>('/exams');

    // Sort exams by date (newest first)
    return exams.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  } catch (error) {
    console.error('Error fetching exams:', error);
    throw error;
  }
};

export const getExamById = async (id: string): Promise<Exam | null> => {
  try {
    console.log(`Fetching exam with ID: ${id}`);
    const exam = await apiClient.get<Exam>(`/exams/${id}`);

    if (!exam) {
      throw new Error('Exam not found');
    }

    return exam;
  } catch (error) {
    console.error('Error fetching exam:', error);
    return null;
  }
};

export const createExam = async (examData: ExamFormData): Promise<Exam> => {
  try {
    console.log('Creating new exam:', examData);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Set default status if not provided
    const status = examData.status || 'upcoming';

    const result = await apiClient.post<Exam>('/exams', {
      ...examData,
      status
    });

    await logActivity('exam_created', {
      examId: result.id,
      name: examData.name,
      courseId: examData.course_id,
      levelId: examData.level_id
    });

    toast.success('Exam created successfully');

    return result;
  } catch (error: any) {
    console.error('Error creating exam:', error);
    toast.error(error.message || 'Failed to create exam');
    throw error;
  }
};

export const updateExam = async (id: string, examData: Partial<ExamFormData>): Promise<Exam> => {
  try {
    console.log('Updating exam:', id, examData);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const result = await apiClient.put<Exam>(`/exams/${id}`, examData);

    await logActivity('exam_updated', {
      examId: id,
      updates: examData
    });

    toast.success('Exam updated successfully');

    return result;
  } catch (error: any) {
    console.error('Error updating exam:', error);
    toast.error(error.message || 'Failed to update exam');
    throw error;
  }
};

export const deleteExam = async (id: string) => {
  try {
    console.log('Deleting exam:', id);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    await apiClient.delete(`/exams/${id}`);

    await logActivity('exam_deleted', {
      examId: id
    });

    toast.success('Exam deleted successfully');

    return { success: true };
  } catch (error: any) {
    console.error('Error deleting exam:', error);
    toast.error(error.message || 'Failed to delete exam');
    throw error;
  }
};

export const getStudentsForExam = async (examId: string): Promise<any[]> => {
  try {
    console.log(`Fetching students for exam with ID: ${examId}`);

    const students = await apiClient.get<any[]>(`/exams/${examId}/students`);

    return students;
  } catch (error) {
    console.error('Error fetching students for exam:', error);
    throw error;
  }
};

export const addExamResult = async (
  examId: string,
  studentId: string,
  marks: number,
  grade: string,
  remarks?: string
): Promise<ExamResult> => {
  try {
    console.log(`Adding exam result for student ${studentId} in exam ${examId}`);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const resultData = {
      exam_id: examId,
      student_id: studentId,
      marks,
      grade,
      remarks,
      date_recorded: new Date().toISOString(),
      recorded_by: user.id
    };

    const result = await apiClient.post<ExamResult>('/exam-results', resultData);

    await logActivity('exam_result_created', {
      examId,
      studentId,
      marks,
      grade
    });

    toast.success('Exam result added successfully');

    return result;
  } catch (error: any) {
    console.error('Error adding exam result:', error);
    toast.error(error.message || 'Failed to add exam result');
    throw error;
  }
};

export const bulkAddExamResults = async (
  examId: string,
  results: { studentId: string; marks: number; grade: string; remarks?: string }[]
): Promise<{ success: boolean; count: number }> => {
  try {
    console.log(`Adding bulk exam results for exam ${examId}`);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const bulkData = {
      exam_id: examId,
      results: results.map(result => ({
        student_id: result.studentId,
        marks: result.marks,
        grade: result.grade,
        remarks: result.remarks,
        date_recorded: new Date().toISOString(),
        recorded_by: user.id
      }))
    };

    const result = await apiClient.post<{ success: boolean; count: number }>('/exam-results/bulk', bulkData);

    await logActivity('exam_results_bulk_created', {
      examId,
      count: results.length
    });

    toast.success(`Added ${results.length} exam results successfully`);

    return result;
  } catch (error: any) {
    console.error('Error adding bulk exam results:', error);
    toast.error(error.message || 'Failed to add bulk exam results');
    throw error;
  }
};